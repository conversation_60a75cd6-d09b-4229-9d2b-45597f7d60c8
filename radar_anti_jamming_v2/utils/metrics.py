"""
性能评估指标模块
"""
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
import matplotlib
# 设置字体以避免中文显示警告
matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False
from collections import defaultdict
import json
import os
import sys

# 添加固定干扰机导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from env.fixed_intermittent_jammer import FixedIntermittentJammer


@dataclass
class EpisodeResult:
    """单回合结果数据类"""
    episode: int
    radar_reward: float
    jammer_reward: float
    detection_rate: float
    false_alarm_rate: float
    average_snr: float
    frequency_hops: int
    power_usage: float
    jamming_effectiveness: float
    episode_length: int


class PerformanceMetrics:
    """性能评估指标计算器"""
    
    @staticmethod
    def calculate_radar_metrics(detection_results: List[Dict]) -> Dict[str, float]:
        """
        计算雷达性能指标
        
        参数:
            detection_results: 检测结果列表
            
        返回:
            雷达性能指标字典
        """
        if not detection_results:
            return {}
        
        # 基本统计
        detections = [r['detected'] for r in detection_results]
        false_alarms = [r.get('false_alarm', False) for r in detection_results]
        snr_values = [r['snr'] for r in detection_results]
        detection_probs = [r['detection_probability'] for r in detection_results]
        
        # 计算指标
        metrics = {
            'detection_rate': np.mean(detections),
            'false_alarm_rate': np.mean(false_alarms),
            'average_snr': np.mean(snr_values),
            'snr_std': np.std(snr_values),
            'min_snr': np.min(snr_values),
            'max_snr': np.max(snr_values),
            'average_detection_prob': np.mean(detection_probs),
            'total_detections': sum(detections),
            'total_false_alarms': sum(false_alarms),
            'total_samples': len(detection_results)
        }
        
        # 计算ROC相关指标
        if metrics['detection_rate'] > 0 or metrics['false_alarm_rate'] > 0:
            # 简化的性能指标
            metrics['detection_performance'] = metrics['detection_rate'] - metrics['false_alarm_rate']
        
        return metrics
    
    @staticmethod
    def calculate_jammer_metrics(jamming_results: List[Dict]) -> Dict[str, float]:
        """
        计算干扰机性能指标
        
        参数:
            jamming_results: 干扰结果列表
            
        返回:
            干扰机性能指标字典
        """
        if not jamming_results:
            return {}
        
        # 提取干扰效果数据
        interference_effects = [r.get('interference_effect', 0) for r in jamming_results]
        power_usages = [r.get('power_usage', 0) for r in jamming_results]
        stealth_scores = [r.get('stealth_score', 0) for r in jamming_results]
        
        metrics = {
            'average_interference_effect': np.mean(interference_effects),
            'interference_effect_std': np.std(interference_effects),
            'max_interference_effect': np.max(interference_effects) if interference_effects else 0,
            'average_power_usage': np.mean(power_usages),
            'power_efficiency': np.mean(interference_effects) / (np.mean(power_usages) + 1e-6),
            'average_stealth_score': np.mean(stealth_scores),
            'total_power_used': sum(power_usages),
            'total_samples': len(jamming_results)
        }
        
        return metrics
    
    @staticmethod
    def calculate_system_metrics(radar_metrics: Dict, jammer_metrics: Dict) -> Dict[str, float]:
        """
        计算系统整体性能指标
        
        参数:
            radar_metrics: 雷达指标
            jammer_metrics: 干扰机指标
            
        返回:
            系统性能指标字典
        """
        system_metrics = {}
        
        # 对抗平衡性
        if radar_metrics and jammer_metrics:
            radar_performance = radar_metrics.get('detection_performance', 0)
            jammer_performance = jammer_metrics.get('average_interference_effect', 0)
            
            system_metrics['adversarial_balance'] = abs(radar_performance - jammer_performance)
            system_metrics['total_system_performance'] = radar_performance + jammer_performance
        
        # 频谱效率
        if radar_metrics:
            system_metrics['spectrum_efficiency'] = radar_metrics.get('detection_rate', 0) / max(1, radar_metrics.get('false_alarm_rate', 1))
        
        # 能量效率
        if jammer_metrics:
            system_metrics['energy_efficiency'] = jammer_metrics.get('power_efficiency', 0)
        
        return system_metrics


class PerformanceEvaluator:
    """性能评估器"""
    
    def __init__(self, config: Dict):
        """
        初始化性能评估器

        参数:
            config: 评估配置字典
        """
        self.config = config
        self.evaluation_episodes = config.get('evaluation_episodes', 100)
        self.save_detailed_results = config.get('save_detailed_results', True)
        self.use_fixed_jammer = config.get('use_fixed_jammer', False)  # 是否使用固定干扰机

        # 结果存储
        self.episode_results = []
        self.evaluation_history = []

        # 检查是否使用分离报告
        self.separate_reporting = config.get('separate_reporting', False)

        if self.separate_reporting:
            # 分离报告模式
            from .academic_reporter import AcademicReportGenerator

            # 创建学术化报告生成器
            reporting_config = config.get('reporting', {})
            self.report_generator = AcademicReportGenerator(reporting_config)

            # 创建固定基线评估器
            fixed_config = config.get('fixed_baseline', {})
            frequency_range = config.get('frequency_range', (8e9, 12e9))
            self.fixed_jammer = FixedIntermittentJammer(
                frequency_range=frequency_range,
                power_budget=3500.0,
                config=fixed_config
            )

            print("启用分离报告评估模式")
            print(f"   固定基线回合: {fixed_config.get('episodes', 100)}")
            print(f"   智能对抗回合: {config.get('intelligent_adversarial', {}).get('episodes', 100)}")

        else:
            # 传统模式（向后兼容）
            self.use_fixed_jammer = config.get('use_fixed_jammer', False)
            if self.use_fixed_jammer:
                fixed_jammer_config = config.get('fixed_jammer', {})
                frequency_range = config.get('frequency_range', (8e9, 12e9))
                self.fixed_jammer = FixedIntermittentJammer(
                    frequency_range=frequency_range,
                    power_budget=3500.0,
                    config=fixed_jammer_config
                )
                print("使用固定间歇采样干扰机进行评估")
            else:
                self.fixed_jammer = None
            self.report_generator = None
        
    def evaluate(self, radar_agent, jammer_agent, env, num_episodes: Optional[int] = None, episode: Optional[int] = None) -> Dict[str, float]:
        """
        评估智能体性能

        参数:
            radar_agent: 雷达智能体
            jammer_agent: 干扰机智能体
            env: 环境
            num_episodes: 评估回合数
            episode: 当前训练回合数（用于报告）

        返回:
            评估结果字典
        """
        if num_episodes is None:
            num_episodes = self.evaluation_episodes

        if self.separate_reporting:
            # 分离报告模式
            return self._evaluate_with_separate_reporting(radar_agent, jammer_agent, env, num_episodes, episode)
        elif hasattr(self, 'use_fixed_jammer') and self.use_fixed_jammer and self.fixed_jammer is not None:
            # 使用固定干扰机评估
            return self._evaluate_with_fixed_jammer(radar_agent, env, num_episodes)
        else:
            # 使用智能干扰机评估
            return self._evaluate_with_intelligent_jammer(radar_agent, jammer_agent, env, num_episodes)

    def _evaluate_with_intelligent_jammer(self, radar_agent, jammer_agent, env, num_episodes: int) -> Dict[str, float]:
        """使用智能干扰机进行评估（原始方法）"""
        print(f"使用智能干扰机评估，共{num_episodes}回合...")

        # 设置为评估模式
        radar_agent.set_eval_mode()
        jammer_agent.set_eval_mode()

        episode_results = []
        detection_results = []
        jamming_results = []

        for episode in range(num_episodes):
            # 重置环境
            (radar_obs, jammer_obs), info = env.reset()

            episode_radar_reward = 0
            episode_jammer_reward = 0
            episode_detections = []
            episode_jamming = []
            step_count = 0

            done = False
            while not done:
                # 智能体动作选择（评估模式）
                radar_action = radar_agent.get_action(radar_obs, eval_mode=True)
                jammer_action = jammer_agent.get_action(jammer_obs, eval_mode=True)

                # 环境步进
                (next_radar_obs, next_jammer_obs), (radar_reward, jammer_reward), done, truncated, step_info = env.step((radar_action, jammer_action))

                # 累积奖励
                episode_radar_reward += radar_reward
                episode_jammer_reward += jammer_reward

                # 记录检测结果
                if 'detection_result' in step_info:
                    episode_detections.append(step_info['detection_result'])

                # 记录干扰结果
                if 'jamming_result' in step_info:
                    episode_jamming.append(step_info['jamming_result'])

                # 更新观察
                radar_obs, jammer_obs = next_radar_obs, next_jammer_obs
                step_count += 1

                if truncated:
                    done = True

            # 计算回合指标
            episode_metrics = env.get_episode_metrics()

            episode_result = EpisodeResult(
                episode=episode,
                radar_reward=episode_radar_reward,
                jammer_reward=episode_jammer_reward,
                detection_rate=episode_metrics.get('detection_rate', 0),
                false_alarm_rate=episode_metrics.get('false_alarm_rate', 0),
                average_snr=episode_metrics.get('average_snr', 0),
                frequency_hops=episode_metrics.get('frequency_hops_per_step', 0) * step_count,
                power_usage=episode_metrics.get('average_power_usage', 0),
                jamming_effectiveness=episode_metrics.get('jamming_effectiveness', 0),
                episode_length=step_count
            )

            episode_results.append(episode_result)
            detection_results.extend(episode_detections)
            jamming_results.extend(episode_jamming)

            # 进度显示
            if (episode + 1) % max(1, num_episodes // 10) == 0:
                print(f"评估进度: {episode + 1}/{num_episodes}")

        # 🔧 修复：使用加权平均计算最终结果
        return self._process_evaluation_results_weighted(episode_results, detection_results, jamming_results, radar_agent, jammer_agent)

    def _evaluate_with_fixed_jammer(self, radar_agent, env, num_episodes: int) -> Dict[str, float]:
        """使用固定间歇采样干扰机进行评估"""
        print(f"使用固定间歇采样干扰机评估，共{num_episodes}回合...")

        # 设置评估模式
        radar_agent.set_eval_mode()

        episode_results = []
        detection_results = []
        jamming_results = []

        for episode in range(num_episodes):
            # 重置固定干扰机
            self.fixed_jammer.reset()

            # 模拟评估过程（简化版本，因为需要完整环境集成）
            episode_radar_reward = 0
            episode_detections = []
            episode_jamming = []
            step_count = 150  # 固定步数

            # 基于固定干扰机配置动态计算基础性能
            base_performance = self._calculate_base_performance()

            for step in range(step_count):
                # 模拟雷达频率选择
                radar_freq = 8e9 + (12e9 - 8e9) * np.random.random()
                radar_obs = np.random.randn(10)

                # 固定干扰机决策
                jamming_params = self.fixed_jammer.get_jamming_action(radar_obs, radar_freq)

                # 计算检测概率
                if jamming_params.timing_strategy == 'sampling':
                    # 采样阶段，无干扰
                    detection_prob = base_performance + 0.03
                    snr = 15.0 + np.random.normal(0, 1.0)
                else:
                    # 转发阶段，有干扰
                    interference_factor = jamming_params.power / 5000.0
                    detection_prob = base_performance * (1 - 0.25 * interference_factor)
                    snr = 15.0 - 6.0 * interference_factor + np.random.normal(0, 1.5)

                # 检测结果
                detected = np.random.random() < detection_prob
                false_alarm = np.random.random() < 0.02  # 2%虚警率

                # 记录结果
                detection_result = {
                    'detected': detected,
                    'snr': snr,
                    'false_alarm': false_alarm,
                    'frequency': radar_freq,
                    'power': 1000.0
                }
                episode_detections.append(detection_result)

                jamming_result = {
                    'effectiveness': 1.0 - detected,
                    'power': jamming_params.power,
                    'frequency': jamming_params.frequency,
                    'type': jamming_params.jamming_type
                }
                episode_jamming.append(jamming_result)

                episode_radar_reward += 10.0 if detected else -5.0

            # 计算回合指标
            detection_rate = np.mean([d['detected'] for d in episode_detections])
            false_alarm_rate = np.mean([d['false_alarm'] for d in episode_detections])
            average_snr = np.mean([d['snr'] for d in episode_detections])
            jamming_effectiveness = np.mean([j['effectiveness'] for j in episode_jamming])

            episode_result = EpisodeResult(
                episode=episode,
                radar_reward=episode_radar_reward,
                jammer_reward=-episode_radar_reward,
                detection_rate=detection_rate,
                false_alarm_rate=false_alarm_rate,
                average_snr=average_snr,
                frequency_hops=len(set([d['frequency'] for d in episode_detections])),
                power_usage=sum([d['power'] for d in episode_detections]),
                jamming_effectiveness=jamming_effectiveness,
                episode_length=step_count
            )

            episode_results.append(episode_result)
            detection_results.extend(episode_detections)
            jamming_results.extend(episode_jamming)

            # 进度显示
            if (episode + 1) % max(1, num_episodes // 10) == 0:
                print(f"评估进度: {episode + 1}/{num_episodes}")

        # 🔧 修复：使用加权平均计算最终结果
        return self._process_evaluation_results_weighted(episode_results, detection_results, jamming_results, radar_agent, None)

    def _process_evaluation_results(self, episode_results, detection_results, jamming_results, radar_agent, jammer_agent):
        """处理评估结果的通用方法"""
        # 计算整体指标
        radar_metrics = PerformanceMetrics.calculate_radar_metrics(detection_results)

        if jammer_agent is not None:
            jammer_metrics = PerformanceMetrics.calculate_jammer_metrics(jamming_results)
            system_metrics = PerformanceMetrics.calculate_system_metrics(radar_metrics, jammer_metrics)
        else:
            # 固定干扰机情况
            jammer_metrics = {'jamming_effectiveness': np.mean([j.get('effectiveness', 0) for j in jamming_results])}
            system_metrics = {}

        # 计算回合级别指标
        episode_metrics = self._calculate_episode_metrics(episode_results)

        # 合并所有指标
        evaluation_results = {
            **radar_metrics,
            **jammer_metrics,
            **system_metrics,
            **episode_metrics
        }

        # 添加成功率（用于课程学习）
        evaluation_results['radar_success_rate'] = radar_metrics.get('detection_rate', 0)
        evaluation_results['jammer_success_rate'] = 1.0 - radar_metrics.get('detection_rate', 0)

        # 保存结果
        if self.save_detailed_results:
            self.episode_results.extend(episode_results)

        self.evaluation_history.append({
            'timestamp': len(self.evaluation_history),
            'num_episodes': len(episode_results),
            'results': evaluation_results.copy()
        })

        # 恢复训练模式
        radar_agent.set_train_mode()
        if jammer_agent is not None:
            jammer_agent.set_train_mode()

        # 显示评估结果
        jammer_type = "固定间歇采样干扰机" if self.use_fixed_jammer else "智能干扰机"
        print(f"评估完成！雷达成功率: {evaluation_results['radar_success_rate']:.3f}")

        return evaluation_results

    def _process_evaluation_results_weighted(self, episode_results, detection_results, jamming_results, radar_agent, jammer_agent):
        """处理评估结果的加权平均方法"""
        print(f"计算{len(episode_results)}回合的加权平均结果...")

        # 🔧 修复：按回合进行加权平均计算
        total_weight = 0
        weighted_detection_rate = 0
        weighted_false_alarm_rate = 0
        weighted_avg_snr = 0
        weighted_jamming_effectiveness = 0

        # 计算每回合的权重（基于回合长度）
        for episode_result in episode_results:
            weight = episode_result.episode_length  # 使用回合长度作为权重
            total_weight += weight

            weighted_detection_rate += episode_result.detection_rate * weight
            weighted_false_alarm_rate += episode_result.false_alarm_rate * weight
            weighted_avg_snr += episode_result.average_snr * weight
            weighted_jamming_effectiveness += episode_result.jamming_effectiveness * weight

        # 计算加权平均
        if total_weight > 0:
            final_detection_rate = weighted_detection_rate / total_weight
            final_false_alarm_rate = weighted_false_alarm_rate / total_weight
            final_avg_snr = weighted_avg_snr / total_weight
            final_jamming_effectiveness = weighted_jamming_effectiveness / total_weight
        else:
            final_detection_rate = 0
            final_false_alarm_rate = 0
            final_avg_snr = 0
            final_jamming_effectiveness = 0

        print(f"   总权重: {total_weight}")
        print(f"   加权检测率: {final_detection_rate:.3f}")
        print(f"   加权平均SNR: {final_avg_snr:.2f} dB")

        # 构建雷达指标
        radar_metrics = {
            'detection_rate': final_detection_rate,
            'false_alarm_rate': final_false_alarm_rate,
            'average_snr': final_avg_snr,
            'radar_success_rate': final_detection_rate  # 成功率等于检测率
        }

        # 构建干扰机指标
        if jammer_agent is not None:
            jammer_metrics = {
                'jamming_effectiveness': final_jamming_effectiveness,
                'jammer_success_rate': 1.0 - final_detection_rate
            }
            system_metrics = {
                'system_balance': abs(final_detection_rate - 0.5) * 2,  # 系统平衡性
                'adversarial_intensity': final_jamming_effectiveness
            }
        else:
            # 固定干扰机情况
            jammer_metrics = {
                'jamming_effectiveness': final_jamming_effectiveness,
                'jammer_success_rate': 1.0 - final_detection_rate
            }
            system_metrics = {
                'baseline_performance': final_detection_rate,  # 基准性能
                'interference_resistance': 1.0 - final_jamming_effectiveness  # 抗干扰能力
            }

        # 计算回合级别统计
        episode_metrics = self._calculate_episode_metrics_weighted(episode_results)

        # 合并所有指标
        evaluation_results = {
            **radar_metrics,
            **jammer_metrics,
            **system_metrics,
            **episode_metrics
        }

        # 保存结果
        if self.save_detailed_results:
            self.episode_results.extend(episode_results)

        self.evaluation_history.append({
            'timestamp': len(self.evaluation_history),
            'num_episodes': len(episode_results),
            'results': evaluation_results.copy(),
            'weighting_method': 'episode_length_weighted'  # 标记使用了加权方法
        })

        # 恢复训练模式
        radar_agent.set_train_mode()
        if jammer_agent is not None:
            jammer_agent.set_train_mode()

        # 显示评估结果
        jammer_type = "固定间歇采样干扰机" if self.use_fixed_jammer else "智能干扰机"
        print(f"评估完成！雷达成功率: {evaluation_results['radar_success_rate']:.3f}")

        return evaluation_results
    
    def _calculate_episode_metrics(self, episode_results: List[EpisodeResult]) -> Dict[str, float]:
        """计算回合级别指标"""
        if not episode_results:
            return {}

        radar_rewards = [r.radar_reward for r in episode_results]
        jammer_rewards = [r.jammer_reward for r in episode_results]
        episode_lengths = [r.episode_length for r in episode_results]

        return {
            'average_radar_reward': np.mean(radar_rewards),
            'average_jammer_reward': np.mean(jammer_rewards),
            'radar_reward_std': np.std(radar_rewards),
            'jammer_reward_std': np.std(jammer_rewards),
            'average_episode_length': np.mean(episode_lengths),
            'episode_length_std': np.std(episode_lengths),
            'total_episodes_evaluated': len(episode_results)
        }

    def _calculate_episode_metrics_weighted(self, episode_results: List[EpisodeResult]) -> Dict[str, float]:
        """计算加权回合级别指标"""
        if not episode_results:
            return {}

        # 计算加权平均（使用回合长度作为权重）
        total_weight = 0
        weighted_radar_reward = 0
        weighted_jammer_reward = 0

        radar_rewards = [r.radar_reward for r in episode_results]
        jammer_rewards = [r.jammer_reward for r in episode_results]
        episode_lengths = [r.episode_length for r in episode_results]

        for episode_result in episode_results:
            weight = episode_result.episode_length
            total_weight += weight
            weighted_radar_reward += episode_result.radar_reward * weight
            weighted_jammer_reward += episode_result.jammer_reward * weight

        # 计算加权平均
        if total_weight > 0:
            avg_radar_reward = weighted_radar_reward / total_weight
            avg_jammer_reward = weighted_jammer_reward / total_weight
        else:
            avg_radar_reward = 0
            avg_jammer_reward = 0

        return {
            'average_radar_reward': avg_radar_reward,
            'average_jammer_reward': avg_jammer_reward,
            'radar_reward_std': np.std(radar_rewards),
            'jammer_reward_std': np.std(jammer_rewards),
            'average_episode_length': np.mean(episode_lengths),
            'episode_length_std': np.std(episode_lengths),
            'total_episodes_evaluated': len(episode_results),
            'total_weight_used': total_weight
        }
    
    def generate_report(self, save_path: str):
        """
        生成评估报告
        
        参数:
            save_path: 保存路径
        """
        if not self.evaluation_history:
            print("没有评估历史数据，无法生成报告")
            return
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 生成文本报告
        self._generate_text_report(os.path.join(save_path, 'evaluation_report.txt'))
        
        # 生成图表
        self._generate_plots(save_path)
        
        # 保存原始数据
        self._save_raw_data(os.path.join(save_path, 'evaluation_data.json'))
        
        print(f"评估报告已生成: {save_path}")
    
    def _generate_text_report(self, filepath: str):
        """生成文本报告"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("雷达-干扰机对抗系统性能评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            if self.evaluation_history:
                latest_eval = self.evaluation_history[-1]['results']
                
                f.write("最新评估结果:\n")
                f.write("-" * 30 + "\n")
                f.write(f"雷达成功率: {latest_eval.get('radar_success_rate', 0):.3f}\n")
                f.write(f"检测率: {latest_eval.get('detection_rate', 0):.3f}\n")
                f.write(f"虚警率: {latest_eval.get('false_alarm_rate', 0):.3f}\n")
                f.write(f"平均SNR: {latest_eval.get('average_snr', 0):.2f} dB\n")
                f.write(f"干扰效果: {latest_eval.get('average_interference_effect', 0):.3f}\n")
                f.write(f"功率效率: {latest_eval.get('power_efficiency', 0):.3f}\n")
                f.write(f"平均雷达奖励: {latest_eval.get('average_radar_reward', 0):.2f}\n")
                f.write(f"平均干扰机奖励: {latest_eval.get('average_jammer_reward', 0):.2f}\n")
                f.write("\n")
            
            # 历史趋势
            if len(self.evaluation_history) > 1:
                f.write("性能趋势:\n")
                f.write("-" * 30 + "\n")
                
                radar_success_rates = [eval_data['results'].get('radar_success_rate', 0) 
                                     for eval_data in self.evaluation_history]
                
                f.write(f"雷达成功率趋势: {radar_success_rates[-5:]}\n")
                f.write(f"最佳雷达成功率: {max(radar_success_rates):.3f}\n")
                f.write(f"最差雷达成功率: {min(radar_success_rates):.3f}\n")
    
    def _generate_plots(self, save_path: str):
        """生成性能图表"""
        if not self.evaluation_history:
            return
        
        # 设置图表样式
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('seaborn')
        
        # 提取数据
        timestamps = [eval_data['timestamp'] for eval_data in self.evaluation_history]
        radar_success_rates = [eval_data['results'].get('radar_success_rate', 0) 
                             for eval_data in self.evaluation_history]
        detection_rates = [eval_data['results'].get('detection_rate', 0) 
                         for eval_data in self.evaluation_history]
        false_alarm_rates = [eval_data['results'].get('false_alarm_rate', 0) 
                           for eval_data in self.evaluation_history]
        avg_snr = [eval_data['results'].get('average_snr', 0) 
                  for eval_data in self.evaluation_history]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Radar-Jammer Adversarial System Performance Evaluation', fontsize=16)
        
        # 成功率趋势
        axes[0, 0].plot(timestamps, radar_success_rates, 'b-', linewidth=2, label='Radar Success Rate')
        axes[0, 0].set_title('Radar Success Rate Trend')
        axes[0, 0].set_xlabel('Evaluation Count')
        axes[0, 0].set_ylabel('Success Rate')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].legend()
        
        # 检测性能
        axes[0, 1].plot(timestamps, detection_rates, 'g-', linewidth=2, label='Detection Rate')
        axes[0, 1].plot(timestamps, false_alarm_rates, 'r-', linewidth=2, label='False Alarm Rate')
        axes[0, 1].set_title('Detection Performance')
        axes[0, 1].set_xlabel('Evaluation Count')
        axes[0, 1].set_ylabel('Rate')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
        
        # SNR趋势
        axes[1, 0].plot(timestamps, avg_snr, 'm-', linewidth=2)
        axes[1, 0].set_title('Average SNR Trend')
        axes[1, 0].set_xlabel('Evaluation Count')
        axes[1, 0].set_ylabel('SNR (dB)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 奖励分布（如果有回合数据）
        if self.episode_results:
            radar_rewards = [r.radar_reward for r in self.episode_results[-100:]]  # 最近100回合
            axes[1, 1].hist(radar_rewards, bins=20, alpha=0.7, color='blue', label='Radar Reward')
            axes[1, 1].set_title('Recent 100 Episodes Reward Distribution')
            axes[1, 1].set_xlabel('Reward Value')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'performance_plots.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _save_raw_data(self, filepath: str):
        """保存原始评估数据"""
        data = {
            'evaluation_history': self.evaluation_history,
            'config': self.config
        }
        
        # 如果有详细回合数据，也保存（但限制数量以避免文件过大）
        if self.episode_results:
            data['recent_episode_results'] = [
                {
                    'episode': r.episode,
                    'radar_reward': r.radar_reward,
                    'jammer_reward': r.jammer_reward,
                    'detection_rate': r.detection_rate,
                    'false_alarm_rate': r.false_alarm_rate,
                    'average_snr': r.average_snr
                } for r in self.episode_results[-1000:]  # 最近1000回合
            ]
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def get_latest_results(self) -> Dict[str, float]:
        """获取最新评估结果"""
        if self.evaluation_history:
            return self.evaluation_history[-1]['results']
        return {}

    def _evaluate_with_separate_reporting(self, radar_agent, jammer_agent, env, num_episodes: int, episode: Optional[int] = None) -> Dict[str, float]:
        """
        使用分离报告模式进行评估

        参数:
            radar_agent: 雷达智能体
            jammer_agent: 干扰机智能体
            env: 环境
            num_episodes: 总评估回合数
            episode: 当前训练回合数

        返回:
            分离报告评估结果
        """
        import time

        # 获取配置
        fixed_config = self.config.get('fixed_baseline', {})
        intel_config = self.config.get('intelligent_adversarial', {})

        fixed_episodes = fixed_config.get('episodes', num_episodes // 2)
        intel_episodes = intel_config.get('episodes', num_episodes // 2)

        # 1. 固定基线评估
        print("开始固定基线评估...")
        start_time = time.time()
        fixed_results = self._evaluate_with_fixed_jammer(radar_agent, env, fixed_episodes)
        fixed_time = (time.time() - start_time) / 60  # 转换为分钟
        fixed_results['eval_time'] = fixed_time
        fixed_results['episodes'] = fixed_episodes

        # 2. 智能对抗评估
        print("开始智能对抗评估...")
        start_time = time.time()
        intel_results = self._evaluate_with_intelligent_jammer(radar_agent, jammer_agent, env, intel_episodes)
        intel_time = (time.time() - start_time) / 60  # 转换为分钟
        intel_results['eval_time'] = intel_time
        intel_results['episodes'] = intel_episodes

        # 3. 生成学术化报告
        if self.report_generator and episode is not None:
            self.report_generator.generate_console_report(fixed_results, intel_results, episode)

        # 4. 返回分离结果
        return {
            'fixed_baseline': fixed_results,
            'intelligent_adversarial': intel_results,
            'evaluation_type': 'separate_reporting',
            'total_episodes': fixed_episodes + intel_episodes,
            'total_time': fixed_time + intel_time
        }

    def _calculate_base_performance(self) -> float:
        """
        根据固定干扰机配置动态计算基础性能
        考虑权重分配和各难度级别的干扰强度
        """
        if not hasattr(self, 'fixed_jammer') or self.fixed_jammer is None:
            return 0.85  # 默认值

        # 获取权重配置
        weights = self.fixed_jammer.difficulty_weights
        total_weight = sum(weights)

        # 根据干扰机参数估算各难度下的雷达成功率
        easy_success_rate = 0.85    # 简单难度下雷达成功率
        medium_success_rate = 0.70  # 中等难度下雷达成功率
        hard_success_rate = 0.50    # 困难难度下雷达成功率

        # 根据实际配置调整成功率
        if hasattr(self.fixed_jammer, 'config'):
            config = self.fixed_jammer.config

            # 根据困难模式的干扰强度调整
            hard_config = config.get('hard', {})
            if hard_config:
                # 采样占空比影响
                duty_cycle = hard_config.get('sampling_duty_cycle', 0.2)
                if duty_cycle > 0.35:
                    hard_success_rate *= 0.7  # 高采样占空比大幅降低成功率
                elif duty_cycle > 0.25:
                    hard_success_rate *= 0.8

                # 功率比例影响
                power_ratio = hard_config.get('power_ratio', 1.0)
                if power_ratio > 1.5:
                    hard_success_rate *= 0.8  # 高功率比例降低成功率
                elif power_ratio > 1.2:
                    hard_success_rate *= 0.9

                # 频率准确度影响
                freq_accuracy = hard_config.get('frequency_accuracy', 0.95)
                if freq_accuracy > 0.98:
                    hard_success_rate *= 0.85  # 高准确度降低成功率
                elif freq_accuracy > 0.96:
                    hard_success_rate *= 0.9

        # 加权平均计算最终基础性能
        weighted_performance = (
            weights[0] * easy_success_rate +
            weights[1] * medium_success_rate +
            weights[2] * hard_success_rate
        ) / total_weight

        return max(0.4, min(0.95, weighted_performance))  # 限制在合理范围内
    
    def clear_history(self):
        """清空评估历史"""
        self.episode_results.clear()
        self.evaluation_history.clear()
