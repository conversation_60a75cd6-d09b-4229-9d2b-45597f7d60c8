#!/usr/bin/env python3
"""
测试调整后的难度设置
"""

import yaml
import sys
import os
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.radar_anti_jamming_env import RadarAntiJammingEnv
from agents.radar_agent import RadarAgent
from agents.jammer_agent import JammerAgent
from utils.metrics import PerformanceEvaluator

def test_adjusted_difficulty():
    """测试调整后的难度"""
    print("🧪 测试调整后的难度设置")
    print("=" * 50)
    
    # 加载配置
    with open('configs/training_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建环境和智能体
    env = RadarAntiJammingEnv(config)
    
    radar_config = config['radar_agent']
    radar_config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'
    radar_agent = RadarAgent(radar_config)
    
    jammer_config = config['jammer_agent'] 
    jammer_config['device'] = 'cuda' if torch.cuda.is_available() else 'cpu'
    jammer_agent = JammerAgent(jammer_config)
    
    # 创建评估器
    evaluation_config = config['evaluation']
    evaluator = PerformanceEvaluator(evaluation_config)
    
    print("✅ 环境和智能体创建成功")
    print(f"   设备: {radar_config['device']}")
    
    # 进行快速评估（减少回合数以节省时间）
    print("\n🔍 进行快速难度测试...")
    
    # 临时修改评估回合数
    original_fixed_episodes = evaluation_config['fixed_baseline']['episodes']
    original_intel_episodes = evaluation_config['intelligent_adversarial']['episodes']
    
    evaluation_config['fixed_baseline']['episodes'] = 20  # 减少到20回合
    evaluation_config['intelligent_adversarial']['episodes'] = 20
    
    try:
        # 执行评估
        results = evaluator.evaluate(radar_agent, jammer_agent, env, num_episodes=40, episode=0)
        
        if results.get('evaluation_type') == 'separate_reporting':
            fixed_results = results['fixed_baseline']
            intel_results = results['intelligent_adversarial']
            
            print(f"\n📊 调整后的难度测试结果:")
            print(f"   固定基线检测率: {fixed_results.get('radar_success_rate', 0):.1%}")
            print(f"   智能对抗胜率: {intel_results.get('radar_success_rate', 0):.1%}")
            
            # 分析结果
            fixed_rate = fixed_results.get('radar_success_rate', 0)
            intel_rate = intel_results.get('radar_success_rate', 0)
            
            print(f"\n🎯 难度评估:")
            if fixed_rate > 0.8:
                print(f"   固定基线: 仍然较容易 ({fixed_rate:.1%})")
            elif fixed_rate > 0.7:
                print(f"   固定基线: 难度适中 ({fixed_rate:.1%}) ✅")
            else:
                print(f"   固定基线: 较困难 ({fixed_rate:.1%})")
                
            if intel_rate > 0.8:
                print(f"   智能对抗: 仍然较容易 ({intel_rate:.1%})")
            elif intel_rate > 0.6:
                print(f"   智能对抗: 难度适中 ({intel_rate:.1%}) ✅")
            else:
                print(f"   智能对抗: 较困难 ({intel_rate:.1%})")
        
    except Exception as e:
        print(f"❌ 评估过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始配置
        evaluation_config['fixed_baseline']['episodes'] = original_fixed_episodes
        evaluation_config['intelligent_adversarial']['episodes'] = original_intel_episodes
    
    print(f"\n📋 调整的参数:")
    print(f"   检测门限: 13.0 → 15.0 dB")
    print(f"   系统损耗: 3.0 → 4.0 dB") 
    print(f"   积分脉冲: 10 → 8")
    print(f"   大气损耗: 0.1 → 0.15 dB/km")
    print(f"   目标距离: 1-50km → 5-45km")
    print(f"   目标RCS: 0.1-100m² → 0.5-50m²")
    print(f"   干扰机功率: 增强10-20%")

if __name__ == "__main__":
    test_adjusted_difficulty()
