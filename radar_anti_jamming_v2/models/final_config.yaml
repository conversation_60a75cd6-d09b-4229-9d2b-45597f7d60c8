device: cuda
early_stopping:
  min_episodes: 2000
  patience: 2000
  target_performance: 0.95
environment:
  freq_grid_size: 100
  frequency_range:
  - 8000000000
  - 12000000000
  jamming_models:
    bandwidth_range:
    - 1000000
    - 100000000
    false_target_count: 5
    forwarding_delay: 1.0e-06
    frequency_range:
    - 8000000000
    - 12000000000
    max_power: 1000.0
    range_gate_pull_off: 100.0
    sampling_duty_cycle: 0.1
  max_steps: 150
  radar_physics:
    atmospheric_loss: 0.1
    detection_threshold: 14.5
    integration_pulses: 10
    system_losses: 3.0
  signal_processing:
    cfar_guard_cells: 4
    cfar_reference_cells: 16
    detection_threshold: 13.0
    fft_size: 1024
    sampling_rate: 100000000
  target_range:
  - 3000
  - 40000
  target_rcs:
  - 0.3
  - 80
  target_velocity:
  - -300
  - 300
  time_window: 20
eval_interval: 100
evaluation:
  final_evaluation:
    enabled: true
    episodes: 100
  fixed_baseline:
    difficulties:
    - easy
    - medium
    - hard
    difficulty_weights:
    - 2
    - 4
    - 4
    easy:
      forwarding_delay: 1.5e-06
      frequency_accuracy: 0.88
      power_ratio: 0.8
      repeat_count: 2
      sampling_duty_cycle: 0.12
    episodes: 50
    hard:
      forwarding_delay: 4.0e-07
      frequency_accuracy: 0.99
      power_ratio: 1.8
      repeat_count: 5
      sampling_duty_cycle: 0.4
    medium:
      forwarding_delay: 1.0e-06
      frequency_accuracy: 0.94
      power_ratio: 1.1
      repeat_count: 3
      sampling_duty_cycle: 0.2
  intelligent_adversarial:
    episodes: 50
    use_current_jammer: true
  reporting:
    decimal_places: 1
    format: academic
    language: chinese
    show_adversarial_analysis: true
    show_detailed_breakdown: true
    show_evaluation_summary: true
    show_performance_trends: false
  save_detailed_results: true
  separate_reporting: true
  total_evaluation_episodes: 100
experiment:
  auto_save: true
  description: 雷达-干扰机双智能体对抗系统基础实验
  memory_limit_gb: 8
  name: radar_jammer_adversarial_v1
  num_workers: 1
  save_frequency: 500
  seed: 42
  tags:
  - radar
  - jamming
  - adversarial
  - multi-agent
jammer_agent:
  action_dims:
  - 4
  - 100
  - 3
  - 3
  alpha: 0.6
  batch_size: 64
  beta: 0.4
  beta_increment: 0.0005
  buffer_size: 15000
  device: cuda
  epsilon_decay: 0.9995
  epsilon_min: 0.01
  epsilon_start: 1.0
  freq_bins: 100
  frequency_range:
  - 8000000000
  - 12000000000
  gamma: 0.99
  hidden_dim: 32
  input_channels: 4
  learning_rate: 0.0001
  max_history_length: 20
  max_power: 1000.0
  power_budget: 10000.0
  target_update_freq: 2000
  tau: 0.01
  use_adaptive: false
log_dir: logs
log_interval: 25
radar_agent:
  action_dims:
  - 100
  - 5
  - 4
  - 3
  alpha: 0.6
  batch_size: 64
  beta: 0.4
  beta_increment: 0.0005
  buffer_size: 30000
  device: cuda
  epsilon_decay: 0.9995
  epsilon_min: 0.01
  epsilon_start: 1.0
  freq_bins: 100
  frequency_range:
  - 8000000000
  - 12000000000
  gamma: 0.99
  hidden_dim: 256
  input_channels: 7
  learning_rate: 0.0001
  max_history_length: 20
  target_update_freq: 2000
  tau: 0.01
  use_adaptive: false
save_dir: models
save_interval: 100
total_episodes: 3000
visualization:
  dpi: 300
  figure_size:
  - 12
  - 8
  plot_interval: 100
  save_plots: true
