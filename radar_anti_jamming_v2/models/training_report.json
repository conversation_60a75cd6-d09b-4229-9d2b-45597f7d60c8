{"training_config": {"total_episodes": 3000, "eval_interval": 100, "save_interval": 100, "log_interval": 25, "device": "cuda", "save_dir": "models", "log_dir": "logs", "early_stopping": {"target_performance": 0.95, "patience": 2000, "min_episodes": 2000}, "environment": {"max_steps": 150, "freq_grid_size": 100, "time_window": 20, "frequency_range": [8000000000, 12000000000], "target_range": [3000, 40000], "target_velocity": [-300, 300], "target_rcs": [0.3, 80], "radar_physics": {"system_losses": 3.0, "integration_pulses": 10, "detection_threshold": 14.5, "atmospheric_loss": 0.1}, "jamming_models": {"max_power": 1000.0, "frequency_range": [8000000000, 12000000000], "bandwidth_range": [1000000, 100000000], "sampling_duty_cycle": 0.1, "forwarding_delay": 1e-06, "false_target_count": 5, "range_gate_pull_off": 100.0}, "signal_processing": {"sampling_rate": 100000000, "fft_size": 1024, "detection_threshold": 13.0, "cfar_guard_cells": 4, "cfar_reference_cells": 16}}, "radar_agent": {"freq_bins": 100, "input_channels": 7, "hidden_dim": 256, "action_dims": [100, 5, 4, 3], "use_adaptive": false, "learning_rate": 0.0001, "gamma": 0.99, "epsilon_start": 1.0, "epsilon_min": 0.01, "epsilon_decay": 0.9995, "batch_size": 64, "buffer_size": 30000, "target_update_freq": 2000, "tau": 0.01, "alpha": 0.6, "beta": 0.4, "beta_increment": 0.0005, "frequency_range": [8000000000, 12000000000], "max_history_length": 20, "device": "cuda"}, "jammer_agent": {"freq_bins": 100, "input_channels": 4, "hidden_dim": 32, "action_dims": [4, 100, 3, 3], "use_adaptive": false, "learning_rate": 0.0001, "gamma": 0.99, "epsilon_start": 1.0, "epsilon_min": 0.01, "epsilon_decay": 0.9995, "batch_size": 64, "buffer_size": 15000, "target_update_freq": 2000, "tau": 0.01, "alpha": 0.6, "beta": 0.4, "beta_increment": 0.0005, "frequency_range": [8000000000, 12000000000], "max_power": 1000.0, "max_history_length": 20, "power_budget": 10000.0, "device": "cuda"}, "evaluation": {"separate_reporting": true, "total_evaluation_episodes": 100, "save_detailed_results": true, "fixed_baseline": {"episodes": 50, "difficulties": ["easy", "medium", "hard"], "difficulty_weights": [2, 4, 4], "easy": {"sampling_duty_cycle": 0.12, "power_ratio": 0.8, "frequency_accuracy": 0.88, "forwarding_delay": 1.5e-06, "repeat_count": 2}, "medium": {"sampling_duty_cycle": 0.2, "power_ratio": 1.1, "frequency_accuracy": 0.94, "forwarding_delay": 1e-06, "repeat_count": 3}, "hard": {"sampling_duty_cycle": 0.4, "power_ratio": 1.8, "frequency_accuracy": 0.99, "forwarding_delay": 4e-07, "repeat_count": 5}}, "intelligent_adversarial": {"episodes": 50, "use_current_jammer": true}, "reporting": {"format": "academic", "language": "chinese", "show_performance_trends": false, "show_detailed_breakdown": true, "show_adversarial_analysis": true, "show_evaluation_summary": true, "decimal_places": 1}, "final_evaluation": {"enabled": true, "episodes": 100}}, "visualization": {"save_plots": true, "plot_interval": 100, "figure_size": [12, 8], "dpi": 300}, "experiment": {"name": "radar_jammer_adversarial_v1", "description": "雷达-干扰机双智能体对抗系统基础实验", "tags": ["radar", "jamming", "adversarial", "multi-agent"], "seed": 42, "num_workers": 1, "memory_limit_gb": 8, "auto_save": true, "save_frequency": 500}}, "total_episodes": 3000, "total_time_seconds": 389.2475600242615, "final_evaluation": {"fixed_baseline": {"detection_rate": 0.5465333333333333, "false_alarm_rate": 0.020933333333333335, "average_snr": 14.259598144662823, "radar_success_rate": 0.5465333333333333, "jamming_effectiveness": 0.4534666666666667, "jammer_success_rate": 0.4534666666666667, "baseline_performance": 0.5465333333333333, "interference_resistance": 0.5465333333333333, "average_radar_reward": 479.7, "average_jammer_reward": -479.7, "radar_reward_std": 87.9, "jammer_reward_std": 87.9, "average_episode_length": 150.0, "episode_length_std": 0.0, "total_episodes_evaluated": 50, "total_weight_used": 7500, "eval_time": 0.003710512320200602, "episodes": 50}, "intelligent_adversarial": {"detection_rate": 0.7022666666666667, "false_alarm_rate": 0.0, "average_snr": 19.166525118737162, "radar_success_rate": 0.7022666666666667, "jamming_effectiveness": 0.29597375113653757, "jammer_success_rate": 0.2977333333333333, "system_balance": 0.4045333333333334, "adversarial_intensity": 0.29597375113653757, "average_radar_reward": 8255.948938390531, "average_jammer_reward": 8210.925403224974, "radar_reward_std": 7093.9903191612175, "jammer_reward_std": 3883.0761628135306, "average_episode_length": 150.0, "episode_length_std": 0.0, "total_episodes_evaluated": 50, "total_weight_used": 7500, "eval_time": 0.4918260375658671, "episodes": 50}, "evaluation_type": "separate_reporting", "total_episodes": 100, "total_time": 0.4955365498860677}, "best_radar_performance": 0, "training_statistics": {"final_avg_radar_reward": -1182.8452717661035, "final_avg_jammer_reward": 5801.831784414265, "final_avg_episode_length": 150.0}}