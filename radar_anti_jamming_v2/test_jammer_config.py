#!/usr/bin/env python3
"""
测试固定干扰机配置是否正确应用
"""

import yaml
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.fixed_intermittent_jammer import FixedIntermittentJammer, JammingDifficulty

def test_jammer_config():
    """测试固定干扰机配置"""
    print("🔍 测试固定干扰机配置应用")
    print("=" * 50)
    
    # 加载配置
    with open('configs/training_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    fixed_config = config['evaluation']['fixed_baseline']
    
    print("📋 配置文件中的参数:")
    print(f"   权重: {fixed_config['difficulty_weights']}")
    print(f"   简单难度:")
    easy_config = fixed_config['easy']
    for key, value in easy_config.items():
        print(f"     {key}: {value}")
    
    print(f"   中等难度:")
    medium_config = fixed_config['medium']
    for key, value in medium_config.items():
        print(f"     {key}: {value}")
        
    print(f"   困难难度:")
    hard_config = fixed_config['hard']
    for key, value in hard_config.items():
        print(f"     {key}: {value}")
    
    print("\n🔧 创建固定干扰机实例...")
    
    # 创建三个难度的干扰机
    frequency_range = (8e9, 12e9)
    
    easy_jammer = FixedIntermittentJammer(
        frequency_range=frequency_range,
        difficulty=JammingDifficulty.EASY,
        power_budget=3500.0,
        config=fixed_config
    )
    
    medium_jammer = FixedIntermittentJammer(
        frequency_range=frequency_range,
        difficulty=JammingDifficulty.MEDIUM,
        power_budget=3500.0,
        config=fixed_config
    )
    
    hard_jammer = FixedIntermittentJammer(
        frequency_range=frequency_range,
        difficulty=JammingDifficulty.HARD,
        power_budget=3500.0,
        config=fixed_config
    )
    
    print("\n📊 实际应用的参数:")
    
    jammers = [
        ("简单", easy_jammer),
        ("中等", medium_jammer), 
        ("困难", hard_jammer)
    ]
    
    for name, jammer in jammers:
        print(f"   {name}难度:")
        print(f"     采样占空比: {jammer.sampling_duty_cycle:.2f}")
        print(f"     功率效率: {jammer.power_efficiency:.2f}")
        print(f"     跟踪准确率: {jammer.tracking_accuracy:.2f}")
        print(f"     转发延迟: {jammer.forwarding_delay*1e6:.1f}μs")
        print(f"     重复次数: {jammer.repeat_count}")
    
    print(f"\n⚖️ 权重分配:")
    weights = fixed_config['difficulty_weights']
    total_weight = sum(weights)
    print(f"   简单: {weights[0]}/{total_weight} = {weights[0]/total_weight:.1%}")
    print(f"   中等: {weights[1]}/{total_weight} = {weights[1]/total_weight:.1%}")
    print(f"   困难: {weights[2]}/{total_weight} = {weights[2]/total_weight:.1%}")
    
    print(f"\n🎯 分析:")
    if weights[2]/total_weight > 0.6:
        print("   ✅ 困难模式权重占主导 (>60%)")
    else:
        print("   ⚠️ 困难模式权重不够高")
        
    if hard_jammer.sampling_duty_cycle > 0.3:
        print("   ✅ 困难模式采样占空比很高 (>30%)")
    else:
        print("   ⚠️ 困难模式采样占空比可能不够")
        
    if hard_jammer.power_efficiency > 1.5:
        print("   ✅ 困难模式功率效率很高 (>150%)")
    else:
        print("   ⚠️ 困难模式功率效率可能不够")

if __name__ == "__main__":
    test_jammer_config()
