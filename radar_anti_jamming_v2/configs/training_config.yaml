# 雷达-干扰机双智能体对抗系统训练配置

# 基本训练参数
total_episodes: 3000
eval_interval: 100
save_interval: 100
log_interval: 25
device: "cuda"  # cuda 或 cpu
save_dir: "models"
log_dir: "logs"

# 早停配置
early_stopping:
  target_performance: 0.95  # 目标雷达成功率
  patience: 2000  # 无改善的最大回合数
  min_episodes: 2000  # 最少训练回合数

# 环境配置
environment:
  max_steps: 150
  freq_grid_size: 100
  time_window: 20
  frequency_range: [8000000000, 12000000000]  # X波段 8-12 GHz
  target_range: [3000, 40000]  # 目标距离范围 3-40km - 适度调整
  target_velocity: [-300, 300]  # 目标速度范围 ±300m/s
  target_rcs: [0.3, 80]  # 目标RCS范围 0.3-80m² - 适度调整
  
  # 雷达物理模型参数
  radar_physics:
    system_losses: 3.0  # 系统损耗 (dB) - 恢复原值
    integration_pulses: 10  # 积分脉冲数 - 恢复原值
    detection_threshold: 14.5  # 检测门限 (dB) - 只适度提高
    atmospheric_loss: 0.1  # 大气损耗 (dB/km) - 恢复原值
  
  # 干扰模型参数
  jamming_models:
    max_power: 1000.0  # 最大干扰功率 (W)
    frequency_range: [8000000000, 12000000000]
    bandwidth_range: [1000000, 100000000]
    sampling_duty_cycle: 0.1  # 间歇采样占空比
    forwarding_delay: 0.000001  # 转发延迟 (s)
    false_target_count: 5  # 假目标数量
    range_gate_pull_off: 100.0  # 距离门拖引 (m)
  
  # 信号处理参数
  signal_processing:
    sampling_rate: 100000000  # 采样率 (Hz)
    fft_size: 1024  # FFT点数
    detection_threshold: 13.0  # 检测门限 (dB)
    cfar_guard_cells: 4  # CFAR保护单元数
    cfar_reference_cells: 16  # CFAR参考单元数

# 雷达智能体配置
radar_agent:
  # 网络参数
  freq_bins: 100
  input_channels: 7
  hidden_dim: 256
  action_dims: [100, 5, 4, 3]  # [频率, 脉宽, 子脉冲, 时延]
  use_adaptive: false  # 是否使用自适应网络
  
  # 训练参数
  learning_rate: 0.0001
  gamma: 0.99
  epsilon_start: 1.0
  epsilon_min: 0.01
  epsilon_decay: 0.9995
  batch_size: 64
  buffer_size: 30000
  target_update_freq: 2000
  tau: 0.01  # 软更新系数
  
  # 优先经验回放参数
  alpha: 0.6
  beta: 0.4
  beta_increment: 0.0005
  
  # 其他参数
  frequency_range: [8000000000, 12000000000]
  max_history_length: 20
  device: "cuda"

# 干扰机智能体配置
jammer_agent:
  # 网络参数
  freq_bins: 100
  input_channels: 4
  hidden_dim: 32
  action_dims: [4, 100, 3, 3]  # [干扰类型, 频率, 功率, 时机]
  use_adaptive: false
  
  # 训练参数
  learning_rate: 0.0001
  gamma: 0.99
  epsilon_start: 1.0
  epsilon_min: 0.01
  epsilon_decay: 0.9995
  batch_size: 64
  buffer_size: 15000
  target_update_freq: 2000
  tau: 0.01
  
  # 优先经验回放参数
  alpha: 0.6
  beta: 0.4
  beta_increment: 0.0005
  
  # 干扰机特定参数
  frequency_range: [8000000000, 12000000000]
  max_power: 1000.0
  max_history_length: 20
  power_budget: 10000.0  # 总功率预算
  device: "cuda"



# 性能评估配置
evaluation:
  # 分离报告配置
  separate_reporting: true
  total_evaluation_episodes: 100
  save_detailed_results: true

  # 固定基线评估配置
  fixed_baseline:
    episodes: 50
    difficulties: [easy, medium, hard]
    difficulty_weights: [2, 4, 4]  # 平衡权重分配

    # 简单难度配置
    easy:
      sampling_duty_cycle: 0.12    # 12%采样占空比 - 进一步增加
      power_ratio: 0.8             # 80%功率比例 - 进一步增加
      frequency_accuracy: 0.88     # 88%频率准确度 - 进一步增加
      forwarding_delay: 1.5e-6     # 1.5μs转发延迟 - 进一步减少
      repeat_count: 2              # 改为2次转发

    # 中等难度配置
    medium:
      sampling_duty_cycle: 0.20    # 20%采样占空比 - 进一步增加
      power_ratio: 1.1             # 110%功率比例 - 进一步增加
      frequency_accuracy: 0.94     # 94%频率准确度 - 进一步增加
      forwarding_delay: 1.0e-6     # 1.0μs转发延迟 - 进一步减少
      repeat_count: 3              # 改为3次转发

    # 困难难度配置
    hard:
      sampling_duty_cycle: 0.40    # 40%采样占空比 - 大幅增加
      power_ratio: 1.8             # 180%功率比例 - 大幅增加
      frequency_accuracy: 0.99     # 99%频率准确度 - 接近完美
      forwarding_delay: 0.4e-6     # 0.4μs转发延迟 - 大幅减少
      repeat_count: 5              # 改为5次转发

  # 智能对抗评估配置
  intelligent_adversarial:
    episodes: 50
    use_current_jammer: true

  # 报告格式配置
  reporting:
    format: "academic"
    language: "chinese"
    show_performance_trends: false
    show_detailed_breakdown: true
    show_adversarial_analysis: true
    show_evaluation_summary: true
    decimal_places: 1

  # 最终评估配置
  final_evaluation:
    enabled: true  # 是否在训练结束后进行最终评估
    episodes: 100  # 最终评估的回合数

# 可视化配置
visualization:
  save_plots: true
  plot_interval: 100
  figure_size: [12, 8]
  dpi: 300

# 实验配置
experiment:
  name: "radar_jammer_adversarial_v1"
  description: "雷达-干扰机双智能体对抗系统基础实验"
  tags: ["radar", "jamming", "adversarial", "multi-agent"]
  
  # 随机种子
  seed: 42
  
  num_workers: 1
  
  # 内存管理
  memory_limit_gb: 8
  
  # 检查点
  auto_save: true
  save_frequency: 500
